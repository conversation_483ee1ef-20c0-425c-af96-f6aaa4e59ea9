'use client';

import { Handshake, ShoppingCart, Store, User } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

import { AppRoutes } from '@/constants/core.constants';
import { useRootContext } from '@/root-context';

export default function RootLayoutFooter() {
  const pathname = usePathname();
  const { currentUser } = useRootContext();

  const allNavItems = [
    {
      icon: Store,
      label: 'Marketplace',
      route: AppRoutes.MARKETPLACE,
      active: pathname === AppRoutes.MARKETPLACE,
    },
    {
      icon: Handshake,
      label: 'Secondary market',
      route: AppRoutes.SECONDARY_MARKET,
      active: pathname === AppRoutes.SECONDARY_MARKET,
    },
    {
      icon: ShoppingCart,
      label: 'My Orders',
      route: AppRoutes.ORDERS,
      active: pathname === AppRoutes.ORDERS,
    },
    {
      icon: User,
      label: 'My Profile',
      route: AppRoutes.PROFILE,
      active: pathname === AppRoutes.PROFILE,
    },
  ];

  // Filter out profile item if no current user
  const navItems = currentUser
    ? allNavItems
    : allNavItems.filter((item) => item.route !== AppRoutes.PROFILE);

  return (
    <footer className="fixed bottom-0 left-0 right-0 bg-[#17212b] text-[#f5f5f5] border-t border-[#3a4a5c] z-50 h-16">
      <div
        className={`grid items-center h-full ${
          navItems.length === 4 ? 'grid-cols-4' : 'grid-cols-3'
        }`}
      >
        {navItems.map((item) => {
          const IconComponent = item.icon;
          return (
            <Link
              key={item.route}
              href={item.route}
              className={`flex flex-col items-center gap-1 py-3 h-auto transition-colors ${
                item.active
                  ? 'text-[#6ab2f2]'
                  : 'text-[#708499] hover:text-[#f5f5f5]'
              }`}
            >
              <IconComponent className="w-6 h-6" />
              <span className="text-[10px] font-medium">{item.label}</span>
            </Link>
          );
        })}
      </div>
    </footer>
  );
}
