'use client';

import { RefreshCw } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';

import type { AdminOrderStats } from '@/api/admin-api';
import { getAdminOrders, getAdminOrderStats } from '@/api/admin-api';
import { getUserById } from '@/api/user-api';
import type { OrderEntity, UserEntity } from '@/constants/core.constants';
import { useToast } from '@/hooks/use-toast';

import { AdminOrdersHeader } from './admin-orders-management/admin-orders-header';
import { AdminOrdersStats } from './admin-orders-management/admin-orders-stats';
import { AdminOrdersTable } from './admin-orders-management/admin-orders-table';
import { useAdminOrdersActions } from './admin-orders-management/use-admin-orders-actions';

export function AdminOrdersManagement() {
  const { toast } = useToast();
  const [stats, setStats] = useState<AdminOrderStats | null>(null);
  const [orders, setOrders] = useState<OrderEntity[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingStats, setLoadingStats] = useState(true);
  const [userCache, setUserCache] = useState<Record<string, UserEntity>>({});

  const { handleCancelOrder, handleDeleteOrder } = useAdminOrdersActions();

  const loadStats = useCallback(async () => {
    try {
      setLoadingStats(true);
      const statsData = await getAdminOrderStats();
      setStats(statsData);
    } catch (error) {
      console.error('Error loading admin order stats:', error);
      toast({
        title: 'Error',
        description: 'Failed to load order statistics',
        variant: 'destructive',
      });
    } finally {
      setLoadingStats(false);
    }
  }, [toast]);

  const loadOrders = useCallback(async () => {
    try {
      setLoading(true);
      const result = await getAdminOrders(25);
      setOrders(result.orders);

      const userIds = new Set<string>();
      result.orders.forEach((order) => {
        if (order.buyerId) userIds.add(order.buyerId);
        if (order.sellerId) userIds.add(order.sellerId);
      });

      const userPromises = Array.from(userIds).map(async (userId) => {
        if (!userCache[userId]) {
          try {
            const user = await getUserById(userId);
            return { userId, user };
          } catch (error) {
            console.error(`Error loading user ${userId}:`, error);
            return { userId, user: null };
          }
        }
        return { userId, user: userCache[userId] };
      });

      const userResults = await Promise.all(userPromises);
      const newUserCache = { ...userCache };
      userResults.forEach(({ userId, user }) => {
        if (user) {
          newUserCache[userId] = user;
        }
      });
      setUserCache(newUserCache);
    } catch (error) {
      console.error('Error loading admin orders:', error);
      toast({
        title: 'Error',
        description: 'Failed to load orders',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [toast, userCache]);

  const refreshData = useCallback(async () => {
    await Promise.all([loadStats(), loadOrders()]);
  }, [loadStats, loadOrders]);

  const wrappedHandleCancelOrder = useCallback(
    async (orderId: string) => {
      try {
        await handleCancelOrder(orderId);
        await refreshData();
      } catch {
        // Error handling is done in the hook
      }
    },
    [handleCancelOrder, refreshData],
  );

  const wrappedHandleDeleteOrder = useCallback(
    async (orderId: string) => {
      try {
        await handleDeleteOrder(orderId);
        await refreshData();
      } catch {
        // Error handling is done in the hook
      }
    },
    [handleDeleteOrder, refreshData],
  );

  useEffect(() => {
    refreshData();
  }, [refreshData]);

  if (loadingStats || loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-6 w-6 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <AdminOrdersStats stats={stats} loading={loadingStats} />

      <div className="space-y-4">
        <AdminOrdersHeader onRefresh={refreshData} />

        <AdminOrdersTable
          orders={orders}
          userCache={userCache}
          onCancelOrder={wrappedHandleCancelOrder}
          onDeleteOrder={wrappedHandleDeleteOrder}
        />
      </div>
    </div>
  );
}
