import { Plus } from 'lucide-react';
import { useState } from 'react';

import { But<PERSON> } from '@/components/ui/button';

import { AdminOrdersBatchCreateModal } from './admin-orders-batch-create-modal';

interface AdminOrdersHeaderProps {
  onRefresh: () => Promise<void>;
}

export function AdminOrdersHeader({ onRefresh }: AdminOrdersHeaderProps) {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  const handleBatchCreateSuccess = async () => {
    setIsCreateModalOpen(false);
    await onRefresh();
  };

  return (
    <>
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Admin Orders</h3>
        <Button onClick={() => setIsCreateModalOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Create Admin Orders
        </Button>
      </div>

      <AdminOrdersBatchCreateModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSuccess={handleBatchCreateSuccess}
      />
    </>
  );
}
