import * as admin from "firebase-admin";
import { OrderEntity, OrderStatus, UserRole } from "../types";
import {
  spendLockedFunds,
  updateUserBalance,
  unlockFunds,
} from "./balance-service";
import {
  getAppConfig,
  applyFeeToMarketplaceRevenue,
  applyFixedCancelOrderFee,
} from "./fee-service";
import { BPS_DIVISOR } from "../constants";
import { safeMultiply, safeDivide, safeSubtract } from "../utils";

export async function processOrderCancellation(
  order: OrderEntity,
  cancellingUserId: string
) {
  const db = admin.firestore();
  const config = await getAppConfig();

  // Check if the cancelling user is an admin
  const cancellingUserDoc = await db
    .collection("users")
    .doc(cancellingUserId)
    .get();
  const cancellingUser = cancellingUserDoc.exists
    ? cancellingUserDoc.data()
    : null;
  const isAdminCancelling = cancellingUser?.role === "admin";

  const hasBothParticipants = Boolean(order.buyerId && order.sellerId);
  const isActiveSinglePersonOrder =
    order.status === OrderStatus.ACTIVE && !hasBothParticipants;
  const isPaidTwoPersonOrder =
    order.status === OrderStatus.PAID && hasBothParticipants;

  if (isActiveSinglePersonOrder) {
    return await processSinglePersonCancellation(
      order,
      cancellingUserId,
      config,
      db,
      isAdminCancelling
    );
  } else if (isPaidTwoPersonOrder) {
    return await processTwoPersonCancellation(
      order,
      cancellingUserId,
      config,
      db,
      isAdminCancelling
    );
  } else {
    throw new Error(
      `Order ${order.id} is not in a valid state for cancellation`
    );
  }
}

async function processSinglePersonCancellation(
  order: OrderEntity,
  cancellingUserId: string,
  config: any,
  db: admin.firestore.Firestore,
  isAdminCancelling: boolean
) {
  const buyerLockPercentage = config?.buyer_lock_percentage as number;
  const sellerLockPercentage = config?.seller_lock_percentage as number;

  // Calculate locked amounts to unlock
  const buyerLockedAmount = safeMultiply(order.price, buyerLockPercentage);
  const sellerLockedAmount = safeMultiply(order.price, sellerLockPercentage);

  // Unlock funds for the cancelling user
  if (order.buyerId === cancellingUserId) {
    await unlockFunds(order.buyerId, buyerLockedAmount);
  } else if (order.sellerId === cancellingUserId) {
    await unlockFunds(order.sellerId, sellerLockedAmount);
  }

  // Apply fixed cancellation fee only if not admin
  const feeApplied = isAdminCancelling
    ? 0
    : await applyFixedCancelOrderFee(cancellingUserId);

  // Update order status to cancelled
  await db.collection("orders").doc(order.id).update({
    status: OrderStatus.CANCELLED,
    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
  });

  const feeMessage =
    feeApplied > 0
      ? ` A cancellation fee of ${feeApplied} TON was applied.`
      : "";

  const adminMessage = isAdminCancelling
    ? " (Admin cancellation - no fees applied)"
    : "";

  return {
    success: true,
    message: `Order cancelled successfully. Locked funds have been released.${feeMessage}${adminMessage}`,
    feeApplied,
    feeType: "fixed",
  };
}

async function processTwoPersonCancellation(
  order: OrderEntity,
  cancellingUserId: string,
  config: any,
  db: admin.firestore.Firestore,
  isAdminCancelling: boolean
) {
  const cancelFeePercentage = config?.cancel_order_fee as number;
  const sellerLockPercentage = config?.seller_lock_percentage as number;

  // Calculate amounts
  const sellerLockedAmount = safeMultiply(order.price, sellerLockPercentage);

  // If admin is cancelling, no fees are applied - just unlock collateral
  if (isAdminCancelling) {
    // Unlock collateral for both parties without any penalties
    if (order.buyerId) {
      await unlockFunds(order.buyerId, order.price);
    }
    if (order.sellerId) {
      await unlockFunds(order.sellerId, sellerLockedAmount);
    }

    // Update order status to cancelled
    await db.collection("orders").doc(order.id).update({
      status: OrderStatus.CANCELLED,
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    return {
      success: true,
      message:
        "Order cancelled by admin. All locked collateral has been released without penalties.",
      feeApplied: 0,
      feeType: "none",
    };
  }

  // Normal cancellation logic for non-admin users
  const marketplaceFee = safeDivide(
    safeMultiply(order.price, cancelFeePercentage),
    BPS_DIVISOR
  );
  const compensationAmount = safeSubtract(order.price, marketplaceFee);

  // Determine who is cancelling and apply appropriate logic
  if (cancellingUserId === order.sellerId) {
    // Seller cancels: seller loses locked amount, buyer gets compensation
    await spendLockedFunds(order.sellerId, sellerLockedAmount);
    await updateUserBalance({
      userId: order.buyerId!,
      sumChange: compensationAmount,
      lockedChange: 0,
    });
  } else if (cancellingUserId === order.buyerId) {
    // Buyer cancels: buyer loses their payment, seller gets compensation
    await spendLockedFunds(order.buyerId, order.price);
    await updateUserBalance({
      userId: order.sellerId!,
      sumChange: compensationAmount,
      lockedChange: 0,
    });
    // Also unlock seller's locked amount
    await unlockFunds(order.sellerId!, sellerLockedAmount);
  }

  // Apply marketplace fee
  if (marketplaceFee > 0) {
    await applyFeeToMarketplaceRevenue(marketplaceFee, "cancel_order_penalty");
  }

  // Delete the order (same as expired orders)
  await db.collection("orders").doc(order.id).delete();

  const cancellerRole =
    cancellingUserId === order.sellerId ? UserRole.SELLER : UserRole.BUYER;
  const compensatedRole =
    cancellingUserId === order.sellerId ? UserRole.BUYER : UserRole.SELLER;

  return {
    success: true,
    message: `Order cancelled by ${cancellerRole}. ${compensatedRole} received ${compensationAmount} TON compensation. Marketplace fee: ${marketplaceFee} TON.`,
    feeApplied: marketplaceFee,
    feeType: "dynamic",
  };
}

export function validateCancellationPermission(
  order: OrderEntity,
  userId: string
) {
  if (order.buyerId !== userId && order.sellerId !== userId) {
    throw new Error(
      "You can only cancel orders where you are the buyer or seller."
    );
  }

  if (order.status === OrderStatus.FULFILLED) {
    throw new Error("Cannot cancel a fulfilled order.");
  }

  if (order.status === OrderStatus.CANCELLED) {
    throw new Error("Order is already cancelled.");
  }

  if (order.status === OrderStatus.GIFT_SENT_TO_RELAYER) {
    throw new Error(
      "Cannot cancel an order where the gift has already been sent to relayer."
    );
  }
}
