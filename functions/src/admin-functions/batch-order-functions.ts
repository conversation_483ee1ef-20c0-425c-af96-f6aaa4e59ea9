import * as admin from "firebase-admin";
import { on<PERSON>all, HttpsError } from "firebase-functions/v2/https";
import { CollectionEntity, OrderEntity, OrderStatus, UserType } from "../types";
import { requireAuthentication } from "../services/auth-middleware";
import { CORS_CONFIG } from "../config";
import { getNextCounterValue } from "../services/counter-service";
import { calculateOrderDeadline } from "../services/deadline-service";

interface BatchCreateOrdersRequest {
  totalOrders: number;
  buyOrdersPercentage: number;
  sellOrdersPercentage: number;
  minPrice: number;
  maxPrice: number;
}

interface BatchCreateOrdersResponse {
  success: boolean;
  message: string;
  createdOrders: number;
  buyOrders: number;
  sellOrders: number;
}

function getRandomPrice(minPrice: number, maxPrice: number): number {
  return Math.round((Math.random() * (maxPrice - minPrice) + minPrice) * 100) / 100;
}

function getRandomElement<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

export const createBatchOrders = onCall<BatchCreateOrdersRequest>(
  { cors: CORS_CONFIG },
  async (request) => {
    const authRequest = requireAuthentication(request);
    const {
      totalOrders,
      buyOrdersPercentage,
      sellOrdersPercentage,
      minPrice,
      maxPrice,
    } = request.data;

    // Validate that the user is an admin
    const db = admin.firestore();
    const userDoc = await db.collection("users").doc(authRequest.auth.uid).get();
    const userData = userDoc.exists ? userDoc.data() : null;

    if (!userData || userData.role !== "admin") {
      throw new HttpsError(
        "permission-denied",
        "Only admin users can create batch orders."
      );
    }

    // Validate input
    if (
      totalOrders <= 0 ||
      buyOrdersPercentage + sellOrdersPercentage !== 100 ||
      minPrice <= 0 ||
      maxPrice <= 0 ||
      minPrice >= maxPrice
    ) {
      throw new HttpsError("invalid-argument", "Invalid input parameters.");
    }

    try {
      // Get all admin users
      const adminQuery = await db
        .collection("users")
        .where("role", "==", "admin")
        .get();

      if (adminQuery.empty) {
        throw new HttpsError("failed-precondition", "No admin users found.");
      }

      const adminUsers = adminQuery.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));

      // Get all collections without launchedAt field
      const collectionsQuery = await db.collection("collections").get();
      const unlaunchedCollections: CollectionEntity[] = [];

      collectionsQuery.forEach((doc) => {
        const collectionData = { id: doc.id, ...doc.data() } as CollectionEntity;
        if (!collectionData.launchedAt) {
          unlaunchedCollections.push(collectionData);
        }
      });

      if (unlaunchedCollections.length === 0) {
        throw new HttpsError(
          "failed-precondition",
          "No unlaunched collections found."
        );
      }

      // Calculate number of buy and sell orders
      const buyOrdersCount = Math.round((totalOrders * buyOrdersPercentage) / 100);
      const sellOrdersCount = totalOrders - buyOrdersCount;

      const createdOrders: OrderEntity[] = [];

      // Create buy orders
      for (let i = 0; i < buyOrdersCount; i++) {
        const randomAdmin = getRandomElement(adminUsers);
        const randomCollection = getRandomElement(unlaunchedCollections);
        const randomPrice = getRandomPrice(minPrice, maxPrice);
        const orderNumber = await getNextCounterValue("order_number");
        const deadline = calculateOrderDeadline(randomCollection);

        const orderData: Omit<OrderEntity, "id"> = {
          number: orderNumber,
          buyerId: randomAdmin.id,
          collectionId: randomCollection.id,
          price: randomPrice,
          status: OrderStatus.ACTIVE,
          owned_gift_id: null,
          ...(deadline && { deadline }),
          secondaryMarketPrice: null,
          createdAt: admin.firestore.FieldValue.serverTimestamp() as any,
          updatedAt: admin.firestore.FieldValue.serverTimestamp() as any,
        };

        const orderRef = await db.collection("orders").add(orderData);
        createdOrders.push({ id: orderRef.id, ...orderData } as OrderEntity);
      }

      // Create sell orders
      for (let i = 0; i < sellOrdersCount; i++) {
        const randomAdmin = getRandomElement(adminUsers);
        const randomCollection = getRandomElement(unlaunchedCollections);
        const randomPrice = getRandomPrice(minPrice, maxPrice);
        const orderNumber = await getNextCounterValue("order_number");
        const deadline = calculateOrderDeadline(randomCollection);

        const orderData: Omit<OrderEntity, "id"> = {
          number: orderNumber,
          sellerId: randomAdmin.id,
          collectionId: randomCollection.id,
          price: randomPrice,
          status: OrderStatus.ACTIVE,
          owned_gift_id: null,
          ...(deadline && { deadline }),
          secondaryMarketPrice: null,
          createdAt: admin.firestore.FieldValue.serverTimestamp() as any,
          updatedAt: admin.firestore.FieldValue.serverTimestamp() as any,
        };

        const orderRef = await db.collection("orders").add(orderData);
        createdOrders.push({ id: orderRef.id, ...orderData } as OrderEntity);
      }

      return {
        success: true,
        message: `Successfully created ${totalOrders} orders (${buyOrdersCount} buy orders, ${sellOrdersCount} sell orders)`,
        createdOrders: totalOrders,
        buyOrders: buyOrdersCount,
        sellOrders: sellOrdersCount,
      };
    } catch (error) {
      console.error("Error creating batch orders:", error);
      throw new HttpsError(
        "internal",
        (error as any).message ?? "Server error while creating batch orders."
      );
    }
  }
);
